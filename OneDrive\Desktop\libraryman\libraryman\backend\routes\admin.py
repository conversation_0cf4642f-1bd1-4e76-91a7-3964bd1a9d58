from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import User, Book, Ebook, College, Department, Circulation, NewsClipping, Category, db
from datetime import datetime, date, timedelta
import pandas as pd
import io
import os
from functools import wraps

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403
        return f(*args, **kwargs)
    return decorated_function

# College Management
@admin_bp.route('/colleges', methods=['GET'])
@admin_required
def get_colleges():
    try:
        colleges = College.query.all()
        return jsonify({
            'colleges': [{
                'id': college.id,
                'name': college.name,
                'code': college.code,
                'created_at': college.created_at.isoformat(),
                'departments_count': len(college.departments)
            } for college in colleges]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/colleges', methods=['POST'])
@admin_required
def create_college():
    try:
        data = request.get_json()
        name = data.get('name')
        code = data.get('code')
        
        if not name or not code:
            return jsonify({'error': 'Name and code are required'}), 400
        
        # Check if college already exists
        existing = College.query.filter_by(name=name).first()
        if existing:
            return jsonify({'error': 'College already exists'}), 400
        
        college = College(name=name, code=code)
        db.session.add(college)
        db.session.commit()
        
        return jsonify({
            'message': 'College created successfully',
            'college': {
                'id': college.id,
                'name': college.name,
                'code': college.code
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Department Management
@admin_bp.route('/departments', methods=['GET'])
@admin_required
def get_departments():
    try:
        college_id = request.args.get('college_id')
        if college_id:
            departments = Department.query.filter_by(college_id=college_id).all()
        else:
            departments = Department.query.all()
        
        return jsonify({
            'departments': [{
                'id': dept.id,
                'name': dept.name,
                'code': dept.code,
                'college_id': dept.college_id,
                'college_name': dept.college.name,
                'created_at': dept.created_at.isoformat()
            } for dept in departments]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/departments', methods=['POST'])
@admin_required
def create_department():
    try:
        data = request.get_json()
        name = data.get('name')
        code = data.get('code')
        college_id = data.get('college_id')
        
        if not name or not code or not college_id:
            return jsonify({'error': 'Name, code, and college_id are required'}), 400
        
        # Check if college exists
        college = College.query.get(college_id)
        if not college:
            return jsonify({'error': 'College not found'}), 404
        
        # Check if department already exists in this college
        existing = Department.query.filter_by(name=name, college_id=college_id).first()
        if existing:
            return jsonify({'error': 'Department already exists in this college'}), 400
        
        department = Department(name=name, code=code, college_id=college_id)
        db.session.add(department)
        db.session.commit()
        
        return jsonify({
            'message': 'Department created successfully',
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'college_id': department.college_id
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Password Reset for Users
@admin_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@admin_required
def reset_user_password(user_id):
    """Reset a user's password to default format (userid+userid)"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Generate new password using the default format
        new_password = User.generate_password(user.user_id)

        # Set the new password
        user.set_password(new_password)

        # Mark as requiring password change on next login
        user.first_login_completed = False

        db.session.commit()

        # Log the password reset event
        print(f"Password reset for user {user.user_id} ({user.role}) by admin at {datetime.utcnow()}")

        return jsonify({
            'message': 'Password reset successfully',
            'user_id': user.user_id,
            'username': user.username,
            'new_password': new_password,
            'password_format': 'userid+userid'
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# User Management
@admin_bp.route('/users', methods=['GET'])
@admin_required
def get_users():
    try:
        role = request.args.get('role')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        query = User.query
        if role:
            query = query.filter_by(role=role)
        
        users = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'users': [{
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'designation': user.designation,
                'dob': user.dob.isoformat() if user.dob else None,
                'validity_date': user.validity_date.isoformat() if user.validity_date else None,
                'college': user.college.name if user.college else None,
                'department': user.department.name if user.department else None,
                'is_active': user.is_active,
                'first_login_completed': user.first_login_completed,
                'created_at': user.created_at.isoformat()
            } for user in users.items],
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Single User Creation
@admin_bp.route('/users', methods=['POST'])
@admin_required
def create_user():
    try:
        data = request.get_json()
        user_id = data.get('user_id')  # Roll number
        name = data.get('name')
        email = data.get('email')
        college_id = data.get('college_id')
        department_id = data.get('department_id')
        designation = data.get('designation')  # student or staff
        dob = data.get('dob')
        validity_date = data.get('validity_date')
        role = data.get('role', 'student')  # Default to student
        batch_from = data.get('batch_from')  # Starting year of batch
        batch_to = data.get('batch_to')      # Ending year of batch

        if not all([user_id, name, email, designation, dob, validity_date]):
            return jsonify({'error': 'All fields are required'}), 400

        # Validate batch fields if provided
        if batch_from and batch_to:
            try:
                batch_from = int(batch_from)
                batch_to = int(batch_to)
                if batch_from > batch_to:
                    return jsonify({'error': 'Batch from year cannot be greater than batch to year'}), 400
                if batch_from < 1900 or batch_to > 2100:
                    return jsonify({'error': 'Invalid batch year range'}), 400
            except ValueError:
                return jsonify({'error': 'Batch years must be valid integers'}), 400

        # Check if user already exists
        existing = User.query.filter_by(user_id=user_id).first()
        if existing:
            return jsonify({'error': 'User ID already exists'}), 400

        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            return jsonify({'error': 'Email already exists'}), 400

        # Generate username and password
        username = User.generate_username(name, user_id)
        password = User.generate_password(user_id)  # Use userid+userid format

        # Parse dates
        dob_date = datetime.strptime(dob, '%Y-%m-%d').date()
        validity_date_obj = datetime.strptime(validity_date, '%Y-%m-%d').date()

        user = User(
            user_id=user_id,
            username=username,
            name=name,
            email=email,
            role=role,
            designation=designation,
            dob=dob_date,
            validity_date=validity_date_obj,
            college_id=college_id,
            department_id=department_id,
            batch_from=batch_from,
            batch_to=batch_to
        )
        user.set_password(password)

        # Admin and librarian users don't need mandatory password change
        if role in ['admin', 'librarian']:
            user.first_login_completed = True

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'message': 'User created successfully',
            'user': {
                'id': user.id,
                'user_id': user.user_id,
                'username': username,
                'password': password,  # Return password for admin to share
                'name': user.name,
                'email': user.email,
                'role': user.role
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Bulk User Upload
@admin_bp.route('/users/bulk', methods=['POST'])
@admin_required
def bulk_create_users():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400

        file = request.files['file']
        college_id = request.form.get('college_id')
        department_id = request.form.get('department_id')

        if not college_id or not department_id:
            return jsonify({'error': 'College and department are required'}), 400

        # Read Excel file
        df = pd.read_excel(file)

        # Validate required columns
        required_columns = ['user_id', 'name', 'email', 'validity_date', 'dob']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'error': f'Excel file must contain columns: {required_columns}'}), 400

        created_users = []
        errors = []

        for index, row in df.iterrows():
            try:
                user_id = str(row['user_id'])
                name = row['name']
                email = row['email']
                dob = pd.to_datetime(row['dob']).date()
                validity_date = pd.to_datetime(row['validity_date']).date()

                # Handle optional batch fields
                batch_from = None
                batch_to = None
                if 'batch_from' in df.columns and pd.notna(row['batch_from']):
                    batch_from = int(row['batch_from'])
                if 'batch_to' in df.columns and pd.notna(row['batch_to']):
                    batch_to = int(row['batch_to'])

                # Check if user already exists
                existing = User.query.filter_by(user_id=user_id).first()
                if existing:
                    errors.append(f"Row {index + 1}: User ID {user_id} already exists")
                    continue

                # Generate username and password
                username = User.generate_username(name, user_id)
                password = User.generate_password(user_id)  # Use userid+userid format

                user = User(
                    user_id=user_id,
                    username=username,
                    name=name,
                    email=email,
                    role='student',
                    designation='student',
                    dob=dob,
                    validity_date=validity_date,
                    college_id=college_id,
                    department_id=department_id,
                    batch_from=batch_from,
                    batch_to=batch_to
                )
                user.set_password(password)

                db.session.add(user)
                created_users.append({
                    'user_id': user_id,
                    'username': username,
                    'password': password,
                    'name': name,
                    'email': email
                })

            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")

        db.session.commit()

        return jsonify({
            'message': f'Successfully created {len(created_users)} users',
            'created_users': created_users,
            'errors': errors
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Download Credentials
@admin_bp.route('/users/credentials', methods=['POST'])
@admin_required
def download_credentials():
    try:
        data = request.get_json()
        users_data = data.get('users', [])

        if not users_data:
            return jsonify({'error': 'No user data provided'}), 400

        # Create DataFrame
        df = pd.DataFrame(users_data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='User Credentials', index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='user_credentials.xlsx'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Category Management
@admin_bp.route('/categories', methods=['GET'])
@admin_required
def get_categories():
    try:
        categories = Category.query.order_by(Category.name).all()
        return jsonify({
            'categories': [{
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'created_at': category.created_at.isoformat() if category.created_at else None
            } for category in categories]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/categories', methods=['POST'])
@admin_required
def create_category():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({'error': 'Category name is required'}), 400

        # Check if category already exists (case-insensitive)
        existing_category = Category.query.filter(
            Category.name.ilike(name)
        ).first()

        if existing_category:
            return jsonify({'error': f'Category "{name}" already exists'}), 400

        # Create new category
        category = Category(
            name=name,
            description=description if description else None,
            created_by=user_id
        )

        db.session.add(category)
        db.session.commit()

        return jsonify({
            'message': 'Category created successfully',
            'category': {
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'created_at': category.created_at.isoformat()
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/categories/<int:category_id>', methods=['PUT'])
@admin_required
def update_category(category_id):
    try:
        category = Category.query.get_or_404(category_id)
        data = request.get_json()

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({'error': 'Category name is required'}), 400

        # Check if another category with this name exists
        existing_category = Category.query.filter(
            Category.name.ilike(name),
            Category.id != category_id
        ).first()

        if existing_category:
            return jsonify({'error': f'Category "{name}" already exists'}), 400

        category.name = name
        category.description = description if description else None

        db.session.commit()

        return jsonify({
            'message': 'Category updated successfully',
            'category': {
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'created_at': category.created_at.isoformat()
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/categories/<int:category_id>', methods=['DELETE'])
@admin_required
def delete_category(category_id):
    try:
        category = Category.query.get_or_404(category_id)

        # Check if category is being used by any books
        books_using_category = Book.query.filter_by(category=category.name).count()
        ebooks_using_category = Ebook.query.filter_by(category=category.name).count()

        if books_using_category > 0 or ebooks_using_category > 0:
            return jsonify({
                'error': f'Cannot delete category "{category.name}" as it is being used by {books_using_category + ebooks_using_category} book(s)'
            }), 400

        db.session.delete(category)
        db.session.commit()

        return jsonify({'message': 'Category deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
