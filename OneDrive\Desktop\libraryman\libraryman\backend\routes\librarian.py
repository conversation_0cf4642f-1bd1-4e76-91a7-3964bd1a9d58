from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import User, Book, Ebook, Circulation, NewsClipping, db
from datetime import datetime, timedelta
from functools import wraps

librarian_bp = Blueprint('librarian', __name__)

def librarian_required(f):
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        if not user or user.role not in ['admin', 'librarian']:
            return jsonify({'error': 'Librarian access required'}), 403
        return f(*args, **kwargs)
    return decorated_function

# Book Management
@librarian_bp.route('/books', methods=['GET'])
@librarian_required
def get_books():
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        
        query = Book.query
        if search:
            query = query.filter(
                Book.title.contains(search) | 
                Book.author.contains(search) |
                Book.isbn.contains(search)
            )
        
        books = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'books': [{
                'id': book.id,
                'isbn': book.isbn,
                'title': book.title,
                'author': book.author,
                'publisher': book.publisher,
                'publication_year': book.publication_year,
                'category': book.category,
                'total_copies': book.total_copies,
                'available_copies': book.available_copies,
                'location': book.location,
                'description': book.description,
                'created_at': book.created_at.isoformat()
            } for book in books.items],
            'pagination': {
                'page': books.page,
                'pages': books.pages,
                'per_page': books.per_page,
                'total': books.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@librarian_bp.route('/books', methods=['POST'])
@librarian_required
def create_book():
    try:
        data = request.get_json()
        
        book = Book(
            isbn=data.get('isbn'),
            title=data.get('title'),
            author=data.get('author'),
            publisher=data.get('publisher'),
            publication_year=data.get('publication_year'),
            category=data.get('category'),
            total_copies=data.get('total_copies', 1),
            available_copies=data.get('total_copies', 1),
            location=data.get('location'),
            description=data.get('description')
        )
        
        db.session.add(book)
        db.session.commit()
        
        return jsonify({
            'message': 'Book created successfully',
            'book': {
                'id': book.id,
                'title': book.title,
                'author': book.author
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Ebook Management
@librarian_bp.route('/ebooks', methods=['GET'])
@librarian_required
def get_ebooks():
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        search = request.args.get('search', '')
        
        query = Ebook.query
        if search:
            query = query.filter(
                Ebook.title.contains(search) | 
                Ebook.author.contains(search)
            )
        
        ebooks = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'ebooks': [{
                'id': ebook.id,
                'title': ebook.title,
                'author': ebook.author,
                'publisher': ebook.publisher,
                'publication_year': ebook.publication_year,
                'category': ebook.category,
                'format': ebook.format,
                'file_size': ebook.file_size,
                'download_count': ebook.download_count,
                'description': ebook.description,
                'created_at': ebook.created_at.isoformat()
            } for ebook in ebooks.items],
            'pagination': {
                'page': ebooks.page,
                'pages': ebooks.pages,
                'per_page': ebooks.per_page,
                'total': ebooks.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@librarian_bp.route('/ebooks', methods=['POST'])
@librarian_required
def create_ebook():
    try:
        data = request.get_json()
        
        ebook = Ebook(
            title=data.get('title'),
            author=data.get('author'),
            publisher=data.get('publisher'),
            publication_year=data.get('publication_year'),
            category=data.get('category'),
            file_path=data.get('file_path'),
            file_size=data.get('file_size'),
            format=data.get('format'),
            description=data.get('description')
        )
        
        db.session.add(ebook)
        db.session.commit()
        
        return jsonify({
            'message': 'Ebook created successfully',
            'ebook': {
                'id': ebook.id,
                'title': ebook.title,
                'author': ebook.author
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Circulation Management
@librarian_bp.route('/circulation/issue', methods=['POST'])
@librarian_required
def issue_book():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        book_id = data.get('book_id')
        days = data.get('days', 14)  # Default 14 days
        
        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Check if book exists and is available
        book = Book.query.get(book_id)
        if not book:
            return jsonify({'error': 'Book not found'}), 404
        
        if book.available_copies <= 0:
            return jsonify({'error': 'Book not available'}), 400
        
        # Create circulation record
        circulation = Circulation(
            user_id=user_id,
            book_id=book_id,
            due_date=datetime.utcnow() + timedelta(days=days)
        )
        
        # Update book availability
        book.available_copies -= 1
        
        db.session.add(circulation)
        db.session.commit()
        
        return jsonify({
            'message': 'Book issued successfully',
            'circulation': {
                'id': circulation.id,
                'due_date': circulation.due_date.isoformat()
            }
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@librarian_bp.route('/circulation/return', methods=['POST'])
@librarian_required
def return_book():
    try:
        data = request.get_json()
        circulation_id = data.get('circulation_id')
        
        circulation = Circulation.query.get(circulation_id)
        if not circulation:
            return jsonify({'error': 'Circulation record not found'}), 404
        
        if circulation.return_date:
            return jsonify({'error': 'Book already returned'}), 400
        
        # Update circulation record
        circulation.return_date = datetime.utcnow()
        circulation.status = 'returned'
        
        # Calculate fine if overdue
        fine = circulation.calculate_fine()
        circulation.fine_amount = fine
        
        # Update book availability
        book = Book.query.get(circulation.book_id)
        book.available_copies += 1
        
        db.session.commit()
        
        return jsonify({
            'message': 'Book returned successfully',
            'fine_amount': fine
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@librarian_bp.route('/circulation', methods=['GET'])
@librarian_required
def get_circulation():
    try:
        status = request.args.get('status', 'issued')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        query = Circulation.query.filter_by(status=status)
        circulations = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'circulations': [{
                'id': circ.id,
                'user_name': circ.user.name,
                'book_title': circ.book.title,
                'issue_date': circ.issue_date.isoformat(),
                'due_date': circ.due_date.isoformat(),
                'return_date': circ.return_date.isoformat() if circ.return_date else None,
                'fine_amount': circ.fine_amount,
                'status': circ.status
            } for circ in circulations.items],
            'pagination': {
                'page': circulations.page,
                'pages': circulations.pages,
                'per_page': circulations.per_page,
                'total': circulations.total
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
